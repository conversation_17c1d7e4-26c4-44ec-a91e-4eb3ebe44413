import React, { useState, useRef, useEffect } from 'react';
import { Button, Input, Modal, Spin } from 'antd';
import type { Form } from '@formily/core/esm/models';
import style from './index.module.less';
import MapDraw from '../MapDraw/index';
import { MapCardExpose, MapPoint, PolygonData, SORN } from '../MapDraw/types';

type Props = {
  operateType: string;
  value: string;
  confirmClick: (val: { x: SORN; y: SORN }) => void;
  drawConfirm: (val: PolygonData[]) => void;
  onChange: (val: string) => void;
  form: Form;
  mapType: 'point' | 'line';
  currentRowIndex?: number; // 当前编辑行的索引，用于排除当前行的点位显示
};

const LocationWithRadius: React.FC<Props> = ({
  operateType, // edit add view draw drawpolygon
  value = '', // value 就是form item的initialValue和value
  confirmClick = () => {}, // 确认选点
  drawConfirm = () => {}, // 确认画多边形
  onChange = () => {}, // onChange 事件
  form,
  mapType,
  currentRowIndex, // 当前编辑行的索引
}) => {
  const [isShowAddObjectModal, setIsShowAddObjectModal] = useState(false); // 标记是否展示新增对象弹窗

  // 地图输入框内容
  const [inputValue, setInputValue] = useState<string>(''); // input 的值
  const [pointList, setPointList] = useState<MapPoint[]>([]); // 点位数据
  const [areaList, setAreaList] = useState<string>(''); // 路线数据
  const [positionVal, setPositionVal] = useState<MapPoint>(); // 当前选中的点位
  const tmapRef: React.MutableRefObject<MapCardExpose | null> = useRef();

  // 关闭弹窗
  const closeAddModal: () => void = () => {
    setIsShowAddObjectModal(false);
  };

  // 选择位置确认回调
  const confirmLocationSelect: () => void = () => {
    // 点位
    if (mapType === 'point') {
      const pointLocation: { x: SORN; y: SORN } = tmapRef.current.getPointData();
      confirmClick(pointLocation);
      onChange?.(`${pointLocation.x},${pointLocation.y}`);
    } else {
      // 区域
      const DrawData: PolygonData[] = tmapRef.current.getDrawData();
      drawConfirm(DrawData);
      onChange?.(JSON.stringify(DrawData));
    }
    setIsShowAddObjectModal(false);
  };

  // 打开地图
  const showMap: () => void = () => {
    const { points, list } = form.values;
    const newList: MapPoint[] = list
      .filter((item: Record<string, string>, index: number) => {
        // 如果有当前行索引，则排除当前行；否则通过坐标值排除（兼容旧逻辑）
        if (typeof currentRowIndex === 'number') {
          return item.pointLocation && index !== currentRowIndex;
        }
        // 兼容逻辑：过滤掉当前正在编辑的 pointLocation，避免在地图上显示重复点位
        return item.pointLocation && item.pointLocation !== value;
      })
      .map((item: Record<string, string>) => {
        const locationList: string[] = String(item.pointLocation).split(',');
        return {
          x: locationList[0],
          y: locationList[1],
          z: 0,
          label: item.pointName || '',
        };
      });
    setAreaList(points);
    setPointList(newList);
    setIsShowAddObjectModal(true);
  };

  useEffect(() => {
    setInputValue(value);
    if (value && value !== '') {
      // 判断地图类型
      if (mapType === 'point') {
        const locationList: string[] = String(value).split(',');
        if (locationList.length >= 2) {
          setPositionVal({
            x: parseFloat(locationList[0]),
            y: parseFloat(locationList[1]),
            z: 0,
          });
        }
      }
      if (mapType === 'line') {
        try {
          // value 在 line 模式下是 JSON 字符串
          setAreaList(value);
        } catch {
          // 解析失败时设置为空数组
          setAreaList('');
        }
      }
    }
  }, [value, mapType]);

  return (
    <div className={style['location-select-container']}>
      <Input disabled className={style['map-select-input']} value={inputValue} />

      <Button className={style['map-select-btn']} type="primary" size="small" onClick={showMap}>
        {operateType === 'add' || operateType === 'edit' || operateType === 'draw'
          ? '地图拾取'
          : '查看'}
      </Button>

      <Modal
        title="标绘位置"
        width="60vw"
        destroyOnClose
        visible={isShowAddObjectModal}
        footer={null}
        onCancel={closeAddModal}
        maskClosable={false}
      >
        <Spin spinning={false}>
          <div className={style['map-locationradiusmodal-content']}>
            <div className={style['map-area']}>
              <MapDraw
                ref={tmapRef}
                operateType={operateType}
                areaList={areaList}
                pointList={pointList}
                positionVal={positionVal}
                onCoordChange={(val) => {
                  setInputValue(`${val.lng},${val.lat}`);
                }}
                mapType={mapType}
              />
            </div>
            <div className={style.footer}>
              <Button className={style['location-selector-cancel-btn']} onClick={closeAddModal}>
                取消
              </Button>
              {(operateType === 'add' || operateType === 'edit') && (
                <Button
                  className={style['confirm-btn']}
                  onClick={confirmLocationSelect}
                  type="primary"
                >
                  确定
                </Button>
              )}
            </div>
          </div>
        </Spin>
      </Modal>
    </div>
  );
};

export default LocationWithRadius;
