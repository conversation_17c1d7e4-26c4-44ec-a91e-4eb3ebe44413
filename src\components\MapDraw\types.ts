export type SORN = string | number;

// 地图初始化点位类型
export interface MapPoint {
  x: SORN;
  y: SORN;
  z?: SORN;
  roll?: SORN;
  label?: string;
}

// 多边形数据类型
export interface PolygonData {
  list: number[];
  color: string;
}

export interface Props {
  areaList?: string;
  pointList?: MapPoint[];
  onCoordChange?: (val: { lng: SORN; lat: SORN }) => void;
  operateType: string;
  // confirmButton?: (props: any) => void;
  cancelButton?: () => void;
  mapType?: 'point' | 'line';
  positionVal?: MapPoint;
}

export interface MapCardExpose {
  /** 重置地图到初始状态 */
  resetMap: (posClear?: boolean) => void;
  /** 销毁地图实例 */
  destroyMap: () => void;
  /** 获取当前绘制的区域数据 */
  getDrawData?: () => Array<{
    list: number[];
    color: string;
  }>;
  /** 获取当前地图选中点 */
  getPointData: () => { x: SORN; y: SORN };
}
