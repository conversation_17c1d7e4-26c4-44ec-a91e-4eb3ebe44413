import {formatTree} from 'yth-ui/es/components/util/treeList'

import {rbacRequest} from '../request'
import moment from 'moment/moment'

/** 查询当前用户信息 */
const getCurrentUser = async () => {
  const resp = await rbacRequest.get('/user/appCurrentUser')

  const {data = []} = resp ?? {}

  return data
}

/** 根据角色id查询用户列表 */
const getUsersByRoleId = async (roleId: string) => {
  const resp = await rbacRequest.get('/role/listUsersByRoleId', {
    params: {
      roleId,
    },
  })

  const {data = []} = resp ?? {}

  return data
}

/** 根据角色ID及部门ID公司ID获取用户 */
const listUsersByRoleId = async (roleId: string, orgIds: string) => {
  const resp = await rbacRequest.get('/user/listUsersByRoleId', {
    params: {
      roleId,
      orgIds,
    },
  })

  const {data = []} = resp ?? {}

  return data
}

/** 查询下级的部门信息 */
const unitTreeByOrgId = async (orgIds: string) => {
  const resp = await rbacRequest.get(`/unit/unitTreeByOrgId/${orgIds}`)
  const {data = []} = resp ?? {}
  return data
}

/** 全量更新角色用户 */
const addRoleUser = async (roleId: string, data: string) => {
  await rbacRequest.put('/role/addRoleUser', {
    params: {
      roleId,
    },
    data,
  })
}

/** 返回当前用户的树形菜单集合 */
const getMenus = async () => {
  const resp = await rbacRequest.get('/function/allFunctionList')

  const {data = []} = resp ?? {}

  return data
}

/** 根据用户权限获取组织列表数据 */
const getUnitList = async () => {
  const resp = await rbacRequest.get('/unit/unitList')

  const {data = []} = resp ?? {}

  return data
}

/** 根据用户权限获取组织树形数据 */
const getUnitTree = async (params = {}) => {
  const resp = await rbacRequest.get('/unit/unitTree', {
    params,
  })

  const {data = []} = resp ?? {}
  const tree = formatTree(data, 'unitType', 'unitName')

  return tree
}


/** 获取部门人员数据，对应 YTHPickUser 人员选项结构 */
const getOrgUsers = async ({id}) => {
  const resp = await rbacRequest.post('/user/userList', {
    data: {unitId: id},
  })

  const {data = []} = resp ?? {}

  return data.map(({id, realName, userCode, unitId}) => ({
    id,
    name: realName,
    type: 'user',
    userCode,
    unitId,
  }))
}

/** 获取组织数据，对应 YTHPickDept 选项结构 */
const getPickDepts = async () => {
  const data = await getUnitTree()

  return formatTree(data, 'unitType', 'unitName')
}

/**
 * 获取公司树
 */

const getCompanyList = async () => {
  const res = await rbacRequest.get('/unit/simpleCompanyTree')
  // console.log(res)
  return formatTree(res.data, 'pId', 'title')
}

/**
 * 获取该公司所有上级公司id集合
 * @param orgId 公司ID
 */
const getUpLevelIds = async ({orgId}) => {
  const res = await rbacRequest.get('/unit/getUpLevelIds', {params: {orgId: orgId}})
  const {data = []} = res ?? {}
  return data
}

/**
 * 当前登录人员信息回填
 * @returns
 */
const putUserInfo = async () => {
  const data:any = await getCurrentUser()
  return {
    org: [{...data.company, type: 'dept', name: data.company.unitName}],
    unit: [{...data.unit, type: 'dept', name: data.unit.unitName}],
    user: [{...data.user, type: 'user', name: data.user.realName}],
    time: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss')
  }
}

export default {
  getCurrentUser,
  getUsersByRoleId,
  listUsersByRoleId,
  addRoleUser,
  getMenus,
  getUnitList,
  getUnitTree,
  getPickDepts,
  getOrgUsers,
  getCompanyList,
  putUserInfo,
  unitTreeByOrgId,
  getUpLevelIds
}
